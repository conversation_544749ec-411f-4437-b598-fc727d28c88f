/** 角色信息 */
export interface RoleInfo {
  /** 角色ID */
  id: number
  /** 角色名称 */
  name: string
  /** 角色编码 */
  code: string
  /** 状态：1-启用，0-禁用 */
  status: number
  /** 删除标志：0-未删除，1-已删除 */
  delFlag: number
  /** 创建时间 */
  createTime: string
  /** 更新时间 */
  updateTime: string
  /** 创建人ID */
  createBy: number
  /** 备注 */
  remark: string
}

/** 角色列表API响应数据 */
export interface RoleListApiResponse {
  /** 响应码 */
  code: number
  /** 响应消息 */
  msg: string
  /** 角色列表数据 */
  data: RoleInfo[]
}

/** 角色搜索参数 */
export interface RoleSearchParams {
  /** 角色名称 */
  name?: string
  /** 角色编码 */
  code?: string
  /** 状态 */
  status?: number | string
}

/** 新增角色请求参数 */
export interface AddRoleRequestData {
  /** 角色名称 */
  name: string
  /** 角色编码 */
  code: string
  /** 状态：1-启用，0-禁用 */
  status: number
  /** 备注 */
  remark?: string
}

/** 新增角色API响应数据 */
export interface AddRoleApiResponse {
  /** 响应码 */
  code: number
  /** 响应消息 */
  msg: string
  /** 新增的角色数据 */
  data: RoleInfo
}

/** 用户角色查询参数 */
export interface UserRoleSearchParams {
  /** 用户ID */
  userId: number
}



/** 用户角色绑定请求参数 */
export interface BindUserRoleRequestData {
  /** 用户ID */
  userId: number
  /** 角色ID */
  roleId: number
}

/** 用户角色绑定API响应数据 */
export interface BindUserRoleApiResponse {
  /** 响应码 */
  code: number
  /** 响应消息 */
  msg: string
  /** 响应数据 */
  data: boolean
}

/** 删除角色请求参数 */
export interface DeleteRoleRequestData {
  /** 角色ID */
  roleId: string
}

/** 批量删除角色请求参数 */
export interface BatchDeleteRoleRequestData {
  /** 角色ID列表 */
  roleIds: string
}

/** 删除角色API响应数据 */
export interface DeleteRoleApiResponse {
  /** 响应码 */
  code: number
  /** 响应消息 */
  msg: string
  /** 响应数据 */
  data: boolean
}
