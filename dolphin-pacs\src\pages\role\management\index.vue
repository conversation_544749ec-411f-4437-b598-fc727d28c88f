<script lang="ts" setup>
import { ref, reactive, onMounted, useTemplateRef } from "vue";
import {
  ElMessage,
  ElMessageBox,
  type FormInstance,
  type FormRules,
} from "element-plus";
import type { VxeGridInstance, VxeGridProps } from "vxe-table";
import type {
  RoleInfo,
  RoleSearchParams,
  AddRoleRequestData,
} from "@/common/apis/roles/type";
import { Search, Refresh } from "@element-plus/icons-vue";
import { getRoleListApi, addRoleApi, deleteRoleApi, batchDeleteRoleApi } from "@/common/apis/roles";

defineOptions({
  // 命名当前组件
  name: "RoleManagement",
});

// 表格实例引用
const xGridDom = useTemplateRef<VxeGridInstance>("xGridDom");

// 表单实例引用
const formRef = useTemplateRef<FormInstance>("formRef");

// 搜索表单数据
const searchForm = reactive<RoleSearchParams>({
  name: "",
  code: "",
  status: "",
});

// 弹窗显示状态
const dialogVisible = ref(false);

// 提交加载状态
const loading = ref(false);

// 新增角色表单数据
const formData = reactive<AddRoleRequestData>({
  name: "",
  code: "",
  status: 1,
  remark: "",
});

// 状态选项
const statusOptions = [
  { label: "全部", value: "" },
  { label: "启用", value: 1 },
  { label: "禁用", value: 0 },
];

// 表单状态选项（不包含"全部"）
const formStatusOptions = [
  { label: "启用", value: 1 },
  { label: "禁用", value: 0 },
];

// 表单验证规则
const formRules: FormRules = {
  name: [
    { required: true, message: "请输入角色名称", trigger: "blur" },
    {
      min: 2,
      max: 20,
      message: "角色名称长度在 2 到 20 个字符",
      trigger: "blur",
    },
  ],
  code: [
    { required: true, message: "请输入角色编码", trigger: "blur" },
    {
      min: 2,
      max: 50,
      message: "角色编码长度在 2 到 50 个字符",
      trigger: "blur",
    },
    {
      pattern: /^[A-Z_][A-Z0-9_]*$/,
      message:
        "角色编码只能包含大写字母、数字和下划线，且以大写字母或下划线开头",
      trigger: "blur",
    },
  ],
  status: [{ required: true, message: "请选择状态", trigger: "change" }],
};

// 获取状态标签类型
const getStatusTagType = (status: number) => {
  return status === 1 ? "success" : "danger";
};

// 获取状态文本
const getStatusText = (status: number) => {
  return status === 1 ? "启用" : "禁用";
};

// 表格配置
const xGridOpt: VxeGridProps = reactive({
  loading: false,
  autoResize: true,
  /** 分页配置项 */
  pagerConfig: {
    align: "right",
  },
  /** 工具栏配置 */
  toolbarConfig: {
    refresh: true,
    custom: true,
    slots: {
      buttons: "toolbar-btns",
    },
  },
  /** 自定义列配置项 */
  customConfig: {
    /** 是否允许列选中  */
    checkMethod: ({ column }: { column: any }) =>
      !["name"].includes(column.field),
  },
  /** 列配置 */
  columns: [
    {
      type: "checkbox",
      width: 50,
    },
    {
      type: "seq",
      width: 70,
      title: "序号",
    },
    {
      field: "name",
      title: "角色名称",
      minWidth: 120,
    },
    {
      field: "code",
      title: "角色编码",
      minWidth: 120,
    },
    {
      field: "status",
      title: "状态",
      width: 100,
      slots: { default: "status-column" },
    },
    {
      field: "createTime",
      title: "创建时间",
      width: 180,
      formatter: ({ cellValue }: { cellValue: string }) => {
        return cellValue ? new Date(cellValue).toLocaleString() : "";
      },
    },
    {
      field: "updateTime",
      title: "更新时间",
      width: 180,
      formatter: ({ cellValue }: { cellValue: string }) => {
        return cellValue ? new Date(cellValue).toLocaleString() : "";
      },
    },
    {
      field: "remark",
      title: "备注",
      minWidth: 150,
      showOverflow: "tooltip",
    },
    {
      title: "操作",
      width: 150,
      fixed: "right",
      slots: { default: "action-column" },
    },
  ],
  /** 数据代理配置项（基于 Promise API） */
  proxyConfig: {
    /** 启用动态序号代理 */
    seq: true,
    /** 是否自动加载，默认为 true */
    autoLoad: true,
    props: {
      total: "total",
    },
    ajax: {
      query: () => {
        xGridOpt.loading = true;
        return new Promise((resolve) => {
          loadRoleData()
            .then((res) => {
              xGridOpt.loading = false;
              resolve(res);
            })
            .catch(() => {
              xGridOpt.loading = false;
              resolve({ total: 0, result: [] });
            });
        });
      },
    },
  },
});

// 角色数据加载函数
const loadRoleData = async () => {
  try {
    const params: RoleSearchParams = {
      name: searchForm.name || undefined,
      code: searchForm.code || undefined,
      status: searchForm.status || undefined,
    };

    const response = await getRoleListApi(params);

    // 打印响应数据结构用于调试
    console.log("角色API响应:", response);

    // 检查响应数据结构
    const roleData = Array.isArray(response.data) ? response.data : [];

    return {
      total: roleData.length,
      result: roleData,
    };
  } catch (error) {
    console.error("获取角色数据失败:", error);
    ElMessage.error("获取角色数据失败，请稍后重试");
    return {
      total: 0,
      result: [],
    };
  }
};

// 查询数据
const handleQuery = () => {
  xGridDom.value?.commitProxy("query");
};

// 重置搜索
const handleReset = () => {
  Object.assign(searchForm, {
    name: "",
    code: "",
    status: "",
  });
  handleQuery();
};

// 新增角色
const handleAdd = () => {
  // 重置表单数据
  Object.assign(formData, {
    name: "",
    code: "",
    status: 1,
    remark: "",
  });
  // 显示弹窗
  dialogVisible.value = true;
};

// 重置表单
const resetForm = () => {
  formRef.value?.resetFields();
  Object.assign(formData, {
    name: "",
    code: "",
    status: 1,
    remark: "",
  });
};

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return;

  try {
    // 表单验证
    await formRef.value.validate();

    loading.value = true;

    // 调用新增角色API
    await addRoleApi(formData);

    ElMessage.success("新增角色成功");

    // 关闭弹窗
    dialogVisible.value = false;

    // 刷新表格数据
    handleQuery();
  } catch (error) {
    console.error("新增角色失败:", error);
    ElMessage.error("新增角色失败，请稍后重试");
  } finally {
    loading.value = false;
  }
};

// 批量删除
const handleBatchDelete = () => {
  const selectRecords = xGridDom.value?.getCheckboxRecords();
  if (!selectRecords || selectRecords.length === 0) {
    ElMessage.warning("请选择要删除的角色");
    return;
  }

  const roleNames = selectRecords.map((record: RoleInfo) => record.name).join("、");
  const roleIds = selectRecords.map((record: RoleInfo) => record.id.toString()).join(",");

  ElMessageBox.confirm(
    `确定要删除以下 ${selectRecords.length} 个角色吗？\n${roleNames}\n删除后不可恢复！`,
    "批量删除确认",
    {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
      showClose: true,
      closeOnClickModal: false,
      closeOnPressEscape: true,
    }
  )
    .then(async () => {
      try {
        await batchDeleteRoleApi(roleIds);
        ElMessage.success(`成功删除 ${selectRecords.length} 个角色`);
        // 刷新表格数据
        handleQuery();
      } catch (error) {
        console.error("批量删除角色失败:", error);
        ElMessage.error("批量删除角色失败，请稍后重试");
      }
    })
    .catch(() => {
      // 用户取消删除，不做任何操作
    });
};

// 编辑角色
const handleEdit = (row: RoleInfo) => {
  ElMessage.info(`编辑角色：${row.name}`);
};

// 删除角色
const handleDelete = (row: RoleInfo) => {
  ElMessageBox.confirm(
    `确定要删除角色 "${row.name}" 吗？删除后不可恢复！`,
    "删除确认",
    {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
      showClose: true,
      closeOnClickModal: false,
      closeOnPressEscape: true,
    }
  )
    .then(async () => {
      try {
        await deleteRoleApi(row.id.toString());
        ElMessage.success("删除角色成功");
        // 刷新表格数据
        handleQuery();
      } catch (error) {
        console.error("删除角色失败:", error);
        ElMessage.error("删除角色失败，请稍后重试");
      }
    })
    .catch(() => {
      // 用户取消删除，不做任何操作
    });
};

onMounted(() => {
  // 组件挂载后可以进行一些初始化操作
});
</script>

<template>
  <div class="role-management">
    <!-- 搜索区域 -->
    <div class="search-section">
      <el-card class="search-card">
        <el-form :model="searchForm" inline>
          <el-form-item label="角色名称">
            <el-input
              v-model="searchForm.name"
              placeholder="请输入角色名称"
              clearable
              style="width: 200px"
            />
          </el-form-item>
          <el-form-item label="角色编码">
            <el-input
              v-model="searchForm.code"
              placeholder="请输入角色编码"
              clearable
              style="width: 200px"
            />
          </el-form-item>
          <el-form-item label="状态">
            <el-select
              v-model="searchForm.status"
              placeholder="请选择状态"
              clearable
              style="width: 120px"
            >
              <el-option
                v-for="option in statusOptions"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" :icon="Search" @click="handleQuery">
              搜索
            </el-button>
            <el-button :icon="Refresh" @click="handleReset"> 重置 </el-button>
          </el-form-item>
        </el-form>
      </el-card>
    </div>

    <!-- 表格区域 -->
    <div class="table-section">
      <el-card class="table-card">
        <vxe-grid ref="xGridDom" v-bind="xGridOpt">
          <!-- 工具栏按钮 -->
          <template #toolbar-btns>
            <vxe-button status="primary" icon="vxe-icon-add" @click="handleAdd">
              新增角色
            </vxe-button>
            <vxe-button
              status="danger"
              icon="vxe-icon-delete"
              @click="handleBatchDelete"
            >
              批量删除
            </vxe-button>
          </template>

          <!-- 状态列 -->
          <template #status-column="{ row, column }">
            <el-tag
              :type="getStatusTagType(row[column.field])"
              effect="light"
              size="small"
            >
              {{ getStatusText(row[column.field]) }}
            </el-tag>
          </template>

          <!-- 操作列 -->
          <template #action-column="{ row }">
            <el-button
              link
              type="primary"
              size="small"
              @click="handleEdit(row)"
            >
              编辑
            </el-button>
            <el-button
              link
              type="danger"
              size="small"
              @click="handleDelete(row)"
            >
              删除
            </el-button>
          </template>
        </vxe-grid>
      </el-card>
    </div>

    <!-- 新增角色弹窗 -->
    <el-dialog
      v-model="dialogVisible"
      title="新增角色"
      width="500px"
      :close-on-click-modal="false"
      @closed="resetForm"
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="100px"
        label-position="right"
      >
        <el-form-item label="角色名称" prop="name">
          <el-input
            v-model="formData.name"
            placeholder="请输入角色名称"
            clearable
          />
        </el-form-item>

        <el-form-item label="角色编码" prop="code">
          <el-input
            v-model="formData.code"
            placeholder="请输入角色编码（如：DOCTOR）"
            clearable
          />
        </el-form-item>

        <el-form-item label="状态" prop="status">
          <el-select
            v-model="formData.status"
            placeholder="请选择状态"
            style="width: 100%"
          >
            <el-option
              v-for="option in formStatusOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="备注">
          <el-input
            v-model="formData.remark"
            type="textarea"
            :rows="3"
            placeholder="请输入备注信息（可选）"
            maxlength="200"
            show-word-limit
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="dialogVisible = false"> 取消 </el-button>
        <el-button type="primary" :loading="loading" @click="handleSubmit">
          确认
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
.role-management {
  padding: 20px;

  .search-section {
    margin-bottom: 20px;

    .search-card {
      :deep(.el-card__body) {
        padding: 20px;
      }
    }
  }

  .table-section {
    .table-card {
      :deep(.el-card__body) {
        padding: 0;
      }
    }
  }
}
</style>
